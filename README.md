# PhotoCC Android

一个基于 Jetpack Compose 的 Android 图片浏览应用，使用 Storage Access Framework (SAF) 获取目录权限，支持多目录浏览和多种显示模式。

## 功能特性

### 核心功能
- **目录选择**: 使用 SAF 选择一个或多个目录
- **会话管理**: 创建、保存、加载和管理浏览会话
- **图片浏览**: 支持单列、双列、多列显示模式
- **权限管理**: 自动处理存储权限请求
- **数据持久化**: 使用 DataStore 保存会话数据

### 技术特性
- **Jetpack Compose**: 现代化的 UI 框架
- **Storage Access Framework**: 安全的文件访问
- **Navigation Compose**: 声明式导航
- **Coil**: 高效的图片加载
- **Kotlin Serialization**: 数据序列化
- **ViewModel**: MVVM 架构模式

## 项目结构

```
app/src/main/java/com/wtb/photocc_android/
├── data/                    # 数据模型
│   ├── ImageItem.kt        # 图片项数据类
│   └── Session.kt          # 会话数据类
├── navigation/             # 导航逻辑
│   └── PhotoCCNavigation.kt
├── repository/             # 数据仓库
│   └── SessionManager.kt   # 会话管理器
├── ui/screens/             # UI 界面
│   ├── HomeScreen.kt       # 主界面
│   ├── DirectorySelectionScreen.kt  # 目录选择界面
│   └── ImageBrowserScreen.kt        # 图片浏览界面
├── utils/                  # 工具类
│   ├── DirectorySelector.kt # 目录选择器
│   └── ImageScanner.kt     # 图片扫描器
├── viewmodel/              # ViewModel
│   └── PhotoCCViewModel.kt
└── MainActivity.kt         # 主活动
```

## 使用流程

1. **启动应用**: 应用会自动请求必要的存储权限
2. **创建会话**: 点击主界面的"创建新会话"按钮
3. **选择目录**: 使用 SAF 选择一个或多个包含图片的目录
4. **输入会话名称**: 为会话指定一个名称
5. **创建会话**: 应用会扫描选定目录中的所有图片
6. **浏览图片**: 在图片浏览界面中查看图片，支持切换显示模式
7. **会话管理**: 在主界面管理已创建的会话

## 支持的图片格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)
- BMP (.bmp)

## 显示模式

- **单列模式**: 每行显示一张图片
- **双列模式**: 每行显示两张图片
- **多列模式**: 每行显示三张图片

## 权限要求

- `READ_EXTERNAL_STORAGE`: 读取外部存储（Android 12 及以下）
- `READ_MEDIA_IMAGES`: 读取媒体图片（Android 13 及以上）

## 构建要求

- Android Studio Arctic Fox 或更高版本
- Kotlin 1.9.0
- Android Gradle Plugin 8.5.1
- 最低 SDK 版本: 29 (Android 10)
- 目标 SDK 版本: 34 (Android 14)

## 构建和运行

1. 克隆项目到本地
2. 使用 Android Studio 打开项目
3. 等待 Gradle 同步完成
4. 连接 Android 设备或启动模拟器
5. 点击运行按钮

## 主要依赖

- Jetpack Compose BOM 2024.04.01
- Navigation Compose 2.7.6
- Lifecycle ViewModel Compose 2.7.0
- Coil Compose 2.5.0
- Accompanist Permissions 0.32.0
- DataStore Preferences 1.0.0
- Kotlinx Serialization JSON 1.6.2
- DocumentFile 1.0.1

## 注意事项

- 应用需要用户手动授予目录访问权限
- 首次扫描大量图片可能需要一些时间
- 会话数据会自动保存到本地存储
- 支持递归扫描子目录中的图片

## 许可证

本项目仅供学习和参考使用。
