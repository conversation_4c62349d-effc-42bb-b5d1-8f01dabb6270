package com.wtb.photocc_android.repository

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.wtb.photocc_android.data.Session
import com.wtb.photocc_android.data.ViewMode
import com.wtb.photocc_android.utils.ImageScanner
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

// DataStore 扩展
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "sessions")

/**
 * Session 管理器
 */
class SessionManager(
    private val context: Context,
    private val imageScanner: ImageScanner
) {
    private val json = Json { 
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    companion object {
        private val SESSIONS_KEY = stringPreferencesKey("sessions")
        private val CURRENT_SESSION_KEY = stringPreferencesKey("current_session")
    }
    
    /**
     * 获取所有会话
     */
    fun getAllSessions(): Flow<List<Session>> {
        return context.dataStore.data.map { preferences ->
            try {
                val sessionsJson = preferences[SESSIONS_KEY] ?: "[]"
                json.decodeFromString<List<Session>>(sessionsJson)
                    .sortedByDescending { it.lastAccessedAt }
            } catch (e: Exception) {
                e.printStackTrace()
                emptyList()
            }
        }
    }
    
    /**
     * 获取当前会话
     */
    fun getCurrentSession(): Flow<Session?> {
        return context.dataStore.data.map { preferences ->
            try {
                val currentSessionJson = preferences[CURRENT_SESSION_KEY]
                if (currentSessionJson != null) {
                    json.decodeFromString<Session>(currentSessionJson)
                } else {
                    null
                }
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }
    
    /**
     * 创建新会话
     */
    suspend fun createSession(
        name: String,
        directoryUris: List<String>
    ): Result<Session> {
        return try {
            // 扫描图片
            val images = imageScanner.scanDirectories(directoryUris)
            
            val session = Session(
                name = name,
                directoryUris = directoryUris,
                images = images
            )
            
            // 保存会话
            saveSession(session)
            setCurrentSession(session)
            
            Result.success(session)
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }
    
    /**
     * 保存会话
     */
    suspend fun saveSession(session: Session) {
        try {
            val currentSessions = getAllSessions().first().toMutableList()
            val existingIndex = currentSessions.indexOfFirst { it.id == session.id }
            
            if (existingIndex >= 0) {
                currentSessions[existingIndex] = session
            } else {
                currentSessions.add(session)
            }
            
            context.dataStore.edit { preferences ->
                preferences[SESSIONS_KEY] = json.encodeToString(currentSessions)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 删除会话
     */
    suspend fun deleteSession(sessionId: String) {
        try {
            val currentSessions = getAllSessions().first().toMutableList()
            currentSessions.removeAll { it.id == sessionId }
            
            context.dataStore.edit { preferences ->
                preferences[SESSIONS_KEY] = json.encodeToString(currentSessions)
                
                // 如果删除的是当前会话，清除当前会话
                val currentSession = getCurrentSession().first()
                if (currentSession?.id == sessionId) {
                    preferences.remove(CURRENT_SESSION_KEY)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 设置当前会话
     */
    suspend fun setCurrentSession(session: Session) {
        try {
            val updatedSession = session.updateLastAccessed()
            
            context.dataStore.edit { preferences ->
                preferences[CURRENT_SESSION_KEY] = json.encodeToString(updatedSession)
            }
            
            // 同时更新会话列表中的记录
            saveSession(updatedSession)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 更新当前会话
     */
    suspend fun updateCurrentSession(updater: (Session) -> Session) {
        try {
            val currentSession = getCurrentSession().first()
            if (currentSession != null) {
                val updatedSession = updater(currentSession)
                setCurrentSession(updatedSession)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 刷新会话图片
     */
    suspend fun refreshSessionImages(sessionId: String): Result<Session> {
        return try {
            val sessions = getAllSessions().first()
            val session = sessions.find { it.id == sessionId }
                ?: return Result.failure(Exception("Session not found"))
            
            val newImages = imageScanner.scanDirectories(session.directoryUris)
            val updatedSession = session.updateImages(newImages)
            
            saveSession(updatedSession)
            
            // 如果是当前会话，也更新当前会话
            val currentSession = getCurrentSession().first()
            if (currentSession?.id == sessionId) {
                setCurrentSession(updatedSession)
            }
            
            Result.success(updatedSession)
        } catch (e: Exception) {
            e.printStackTrace()
            Result.failure(e)
        }
    }
    
    /**
     * 清除所有数据
     */
    suspend fun clearAllData() {
        try {
            context.dataStore.edit { preferences ->
                preferences.clear()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
