package com.wtb.photocc_android.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.activity.result.ActivityResultLauncher
import androidx.documentfile.provider.DocumentFile
import com.wtb.photocc_android.data.export.ExportConfig
import java.io.OutputStream

/**
 * 文件保存工具类，使用 SAF 保存导出的文件
 */
class FileSaver(
    private val context: Context,
    private val onFileSaved: (Uri, String) -> Unit,
    private val onError: (String) -> Unit
) {
    
    private var fileSaverLauncher: ActivityResultLauncher<Intent>? = null
    private var pendingConfig: ExportConfig? = null
    
    /**
     * 注册文件保存器
     */
    fun registerLauncher(launcher: ActivityResultLauncher<Intent>) {
        fileSaverLauncher = launcher
    }
    
    /**
     * 启动文件保存对话框
     */
    fun saveFile(config: ExportConfig) {
        try {
            pendingConfig = config
            
            val intent = Intent(Intent.ACTION_CREATE_DOCUMENT).apply {
                addCategory(Intent.CATEGORY_OPENABLE)
                type = getMimeType(config.format.extension)
                putExtra(Intent.EXTRA_TITLE, config.getFullFileName())
            }
            
            fileSaverLauncher?.launch(intent)
        } catch (e: Exception) {
            onError("无法启动文件保存对话框: ${e.message}")
        }
    }
    
    /**
     * 处理文件保存结果
     */
    fun handleResult(uri: Uri?): OutputStream? {
        val config = pendingConfig
        if (uri != null && config != null) {
            try {
                // 获取持久化权限
                context.contentResolver.takePersistableUriPermission(
                    uri,
                    Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                )
                
                // 验证文件是否可写
                val documentFile = DocumentFile.fromSingleUri(context, uri)
                if (documentFile != null && documentFile.canWrite()) {
                    val outputStream = context.contentResolver.openOutputStream(uri)
                    if (outputStream != null) {
                        onFileSaved(uri, config.getFullFileName())
                        return outputStream
                    } else {
                        onError("无法创建输出流")
                    }
                } else {
                    onError("选择的文件无法写入")
                }
            } catch (e: Exception) {
                onError("处理文件保存失败: ${e.message}")
            }
        } else {
            onError("未选择保存位置")
        }
        
        pendingConfig = null
        return null
    }
    
    /**
     * 根据文件扩展名获取 MIME 类型
     */
    private fun getMimeType(extension: String): String {
        return when (extension.lowercase()) {
            "pdf" -> "application/pdf"
            "png" -> "image/png"
            "jpg", "jpeg" -> "image/jpeg"
            "html" -> "text/html"
            "zip" -> "application/zip"
            "epub" -> "application/epub+zip"
            else -> "application/octet-stream"
        }
    }
    
    /**
     * 检查是否有文件的写入权限
     */
    fun hasWritePermission(uri: Uri): Boolean {
        return try {
            val persistedUris = context.contentResolver.persistedUriPermissions
            persistedUris.any { it.uri == uri && it.isWritePermission }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 释放文件权限
     */
    fun releasePermission(uri: Uri) {
        try {
            context.contentResolver.releasePersistableUriPermission(
                uri,
                Intent.FLAG_GRANT_WRITE_URI_PERMISSION
            )
        } catch (e: Exception) {
            // 忽略错误，可能权限已经被释放
        }
    }
    
    /**
     * 获取所有持久化的文件权限
     */
    fun getPersistedFiles(): List<Uri> {
        return try {
            context.contentResolver.persistedUriPermissions
                .filter { it.isWritePermission }
                .map { it.uri }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 清除待处理的配置
     */
    fun clearPendingConfig() {
        pendingConfig = null
    }
}
