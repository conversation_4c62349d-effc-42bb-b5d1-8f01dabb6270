package com.wtb.photocc_android.utils

import android.content.Context
import android.net.Uri
import androidx.documentfile.provider.DocumentFile
import com.wtb.photocc_android.data.ImageItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 图片扫描器，用于扫描目录中的图片文件
 */
class ImageScanner(private val context: Context) {
    
    /**
     * 扫描指定目录中的所有图片
     */
    suspend fun scanDirectories(directoryUris: List<String>): List<ImageItem> {
        return withContext(Dispatchers.IO) {
            val allImages = mutableListOf<ImageItem>()
            
            directoryUris.forEach { uriString ->
                try {
                    val uri = Uri.parse(uriString)
                    val images = scanDirectory(uri)
                    allImages.addAll(images)
                } catch (e: Exception) {
                    // 记录错误但继续处理其他目录
                    e.printStackTrace()
                }
            }
            
            // 按文件名排序
            allImages.sortedBy { it.name.lowercase() }
        }
    }
    
    /**
     * 扫描单个目录
     */
    private suspend fun scanDirectory(directoryUri: Uri): List<ImageItem> {
        return withContext(Dispatchers.IO) {
            val images = mutableListOf<ImageItem>()
            
            try {
                val documentFile = DocumentFile.fromTreeUri(context, directoryUri)
                if (documentFile != null && documentFile.exists() && documentFile.isDirectory) {
                    scanDirectoryRecursive(documentFile, images)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            
            images
        }
    }
    
    /**
     * 递归扫描目录
     */
    private fun scanDirectoryRecursive(directory: DocumentFile, images: MutableList<ImageItem>) {
        try {
            directory.listFiles().forEach { file ->
                if (file.isFile && isImageFile(file)) {
                    val imageItem = createImageItem(file)
                    if (imageItem != null) {
                        images.add(imageItem)
                    }
                } else if (file.isDirectory) {
                    // 递归扫描子目录
                    scanDirectoryRecursive(file, images)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 检查文件是否为图片
     */
    private fun isImageFile(file: DocumentFile): Boolean {
        val name = file.name?.lowercase() ?: return false
        val mimeType = file.type?.lowercase()
        
        // 检查文件扩展名
        val imageExtensions = setOf("jpg", "jpeg", "png", "gif", "webp", "bmp")
        val hasImageExtension = imageExtensions.any { name.endsWith(".$it") }
        
        // 检查 MIME 类型
        val hasImageMimeType = mimeType?.startsWith("image/") == true
        
        return hasImageExtension || hasImageMimeType
    }
    
    /**
     * 创建 ImageItem
     */
    private fun createImageItem(file: DocumentFile): ImageItem? {
        return try {
            val name = file.name ?: return null
            val uri = file.uri.toString()
            val size = file.length()
            val lastModified = file.lastModified()
            val mimeType = file.type
            
            ImageItem(
                uri = uri,
                name = name,
                size = size,
                lastModified = lastModified,
                mimeType = mimeType
            )
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 获取目录信息
     */
    fun getDirectoryInfo(directoryUri: Uri): DirectoryInfo? {
        return try {
            val documentFile = DocumentFile.fromTreeUri(context, directoryUri)
            if (documentFile != null && documentFile.exists() && documentFile.isDirectory) {
                DirectoryInfo(
                    uri = directoryUri.toString(),
                    name = documentFile.name ?: "Unknown",
                    path = getDisplayPath(directoryUri)
                )
            } else {
                null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 获取目录的显示路径
     */
    private fun getDisplayPath(uri: Uri): String {
        return try {
            // 尝试从 URI 中提取路径信息
            val path = uri.path ?: uri.toString()
            // 简化路径显示
            path.split("/").takeLast(2).joinToString("/")
        } catch (e: Exception) {
            uri.toString()
        }
    }
}

/**
 * 目录信息
 */
data class DirectoryInfo(
    val uri: String,
    val name: String,
    val path: String
)
