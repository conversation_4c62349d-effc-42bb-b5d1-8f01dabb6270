package com.wtb.photocc_android.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.documentfile.provider.DocumentFile

/**
 * 目录选择器，使用 Storage Access Framework
 */
class DirectorySelector(
    private val context: Context,
    private val onDirectorySelected: (Uri) -> Unit,
    private val onError: (String) -> Unit
) {
    
    private var directoryPickerLauncher: ActivityResultLauncher<Intent>? = null
    
    /**
     * 注册目录选择器
     * 需要在 Activity 或 Fragment 中调用
     */
    fun registerLauncher(launcher: ActivityResultLauncher<Intent>) {
        directoryPickerLauncher = launcher
    }
    
    /**
     * 启动目录选择
     */
    fun selectDirectory() {
        try {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE).apply {
                flags = Intent.FLAG_GRANT_READ_URI_PERMISSION or
                        Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION
            }
            directoryPickerLauncher?.launch(intent)
        } catch (e: Exception) {
            onError("无法启动目录选择器: ${e.message}")
        }
    }
    
    /**
     * 处理目录选择结果
     */
    fun handleResult(uri: Uri?) {
        if (uri != null) {
            try {
                // 获取持久化权限
                context.contentResolver.takePersistableUriPermission(
                    uri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
                
                // 验证目录是否可访问
                val documentFile = DocumentFile.fromTreeUri(context, uri)
                if (documentFile != null && documentFile.exists() && documentFile.isDirectory) {
                    onDirectorySelected(uri)
                } else {
                    onError("选择的目录无法访问")
                }
            } catch (e: Exception) {
                onError("处理目录选择失败: ${e.message}")
            }
        } else {
            onError("未选择目录")
        }
    }
    
    /**
     * 检查是否有目录的持久化权限
     */
    fun hasPermission(uri: Uri): Boolean {
        return try {
            val persistedUris = context.contentResolver.persistedUriPermissions
            persistedUris.any { it.uri == uri && it.isReadPermission }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 释放目录权限
     */
    fun releasePermission(uri: Uri) {
        try {
            context.contentResolver.releasePersistableUriPermission(
                uri,
                Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
        } catch (e: Exception) {
            // 忽略错误，可能权限已经被释放
        }
    }
    
    /**
     * 获取所有持久化的目录权限
     */
    fun getPersistedDirectories(): List<Uri> {
        return try {
            context.contentResolver.persistedUriPermissions
                .filter { it.isReadPermission }
                .map { it.uri }
        } catch (e: Exception) {
            emptyList()
        }
    }
}
