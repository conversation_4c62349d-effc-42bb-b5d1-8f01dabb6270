package com.wtb.photocc_android

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.navigation.compose.rememberNavController
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import com.wtb.photocc_android.navigation.PhotoCCNavigation
import com.wtb.photocc_android.ui.theme.PhotoCC_androidTheme
import com.wtb.photocc_android.viewmodel.PhotoCCViewModel

class MainActivity : ComponentActivity() {

    private val viewModel: PhotoCCViewModel by viewModels()

    // 目录选择器
    private val directoryPickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            viewModel.handleDirectoryResult(result.data?.data)
        } else {
            viewModel.handleDirectoryResult(null)
        }
    }

    @OptIn(ExperimentalPermissionsApi::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 注册目录选择器
        viewModel.registerDirectoryLauncher(directoryPickerLauncher)

        setContent {
            PhotoCC_androidTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()

                    // 权限处理
                    val permissionsState = rememberMultiplePermissionsState(
                        permissions = listOf(
                            android.Manifest.permission.READ_EXTERNAL_STORAGE,
                            android.Manifest.permission.READ_MEDIA_IMAGES
                        )
                    )

                    LaunchedEffect(Unit) {
                        if (!permissionsState.allPermissionsGranted) {
                            permissionsState.launchMultiplePermissionRequest()
                        }
                    }

                    PhotoCCNavigation(
                        navController = navController,
                        viewModel = viewModel
                    )
                }
            }
        }
    }
}