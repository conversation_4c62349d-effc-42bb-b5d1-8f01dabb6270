package com.wtb.photocc_android.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.wtb.photocc_android.data.export.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LayoutSelectionScreen(
    initialConfig: LayoutConfig,
    onConfigChange: (LayoutConfig) -> Unit,
    onConfirm: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    var config by remember { mutableStateOf(initialConfig) }
    
    LaunchedEffect(config) {
        onConfigChange(config)
    }
    
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 顶部栏
        TopAppBar(
            title = { Text("排版设置") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(
                    onClick = onConfirm,
                    enabled = config.isValid()
                ) {
                    Icon(Icons.Default.Check, contentDescription = "确认")
                }
            }
        )
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 预设模板选择
            item {
                TemplateSelectionSection(
                    selectedTemplate = config.template,
                    onTemplateSelected = { template ->
                        config = LayoutConfig.fromTemplate(template)
                    }
                )
            }
            
            // 自定义设置（仅在选择自定义模板时显示）
            if (config.template == LayoutTemplate.CUSTOM) {
                item {
                    CustomLayoutSection(
                        config = config,
                        onConfigChange = { newConfig ->
                            config = newConfig
                        }
                    )
                }
            }
            
            // 槽位形状选择
            item {
                SlotShapeSection(
                    selectedShape = config.slotShape,
                    onShapeSelected = { shape ->
                        config = config.copy(slotShape = shape)
                    }
                )
            }
            
            // 填充方式选择
            item {
                FillModeSection(
                    selectedMode = config.fillMode,
                    onModeSelected = { mode ->
                        config = config.copy(fillMode = mode)
                    }
                )
            }
            
            // 样式设置
            item {
                StyleSection(
                    config = config,
                    onConfigChange = { newConfig ->
                        config = newConfig
                    }
                )
            }
            
            // 预览
            item {
                PreviewSection(config = config)
            }
        }
    }
}

@Composable
private fun TemplateSelectionSection(
    selectedTemplate: LayoutTemplate,
    onTemplateSelected: (LayoutTemplate) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "预设模板",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                modifier = Modifier.height(200.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(LayoutTemplate.values().toList()) { template ->
                    TemplateItem(
                        template = template,
                        isSelected = template == selectedTemplate,
                        onClick = { onTemplateSelected(template) }
                    )
                }
            }
        }
    }
}

@Composable
private fun TemplateItem(
    template: LayoutTemplate,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .then(
                if (isSelected) {
                    Modifier.border(
                        2.dp,
                        MaterialTheme.colorScheme.primary,
                        RoundedCornerShape(8.dp)
                    )
                } else Modifier
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 模板预览网格
            TemplatePreview(template = template)
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = template.displayName,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
            )
        }
    }
}

@Composable
private fun TemplatePreview(
    template: LayoutTemplate,
    modifier: Modifier = Modifier
) {
    val gridSize = when (template) {
        LayoutTemplate.FOUR_GRID -> 2
        LayoutTemplate.NINE_GRID -> 3
        LayoutTemplate.SIXTEEN_GRID -> 4
        LayoutTemplate.SINGLE_COLUMN -> 1
        LayoutTemplate.DOUBLE_COLUMN -> 2
        LayoutTemplate.TRIPLE_COLUMN -> 3
        LayoutTemplate.CUSTOM -> 3
    }
    
    val itemCount = when (template) {
        LayoutTemplate.FOUR_GRID -> 4
        LayoutTemplate.NINE_GRID -> 9
        LayoutTemplate.SIXTEEN_GRID -> 16
        LayoutTemplate.SINGLE_COLUMN -> 3
        LayoutTemplate.DOUBLE_COLUMN -> 6
        LayoutTemplate.TRIPLE_COLUMN -> 9
        LayoutTemplate.CUSTOM -> 9
    }
    
    LazyVerticalGrid(
        columns = GridCells.Fixed(gridSize),
        modifier = modifier.size(60.dp),
        horizontalArrangement = Arrangement.spacedBy(2.dp),
        verticalArrangement = Arrangement.spacedBy(2.dp),
        userScrollEnabled = false
    ) {
        items(itemCount) {
            Box(
                modifier = Modifier
                    .aspectRatio(1f)
                    .background(
                        MaterialTheme.colorScheme.outline,
                        RoundedCornerShape(2.dp)
                    )
            )
        }
    }
}

@Composable
private fun CustomLayoutSection(
    config: LayoutConfig,
    onConfigChange: (LayoutConfig) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "自定义设置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 列数设置
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "列数: ${config.columns}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Slider(
                        value = config.columns.toFloat(),
                        onValueChange = { value ->
                            onConfigChange(config.copy(columns = value.toInt()))
                        },
                        valueRange = 1f..6f,
                        steps = 4
                    )
                }

                // 行数设置
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "行数: ${config.rows}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Slider(
                        value = config.rows.toFloat(),
                        onValueChange = { value ->
                            onConfigChange(config.copy(rows = value.toInt()))
                        },
                        valueRange = 1f..10f,
                        steps = 8
                    )
                }
            }
        }
    }
}

@Composable
private fun SlotShapeSection(
    selectedShape: SlotShape,
    onShapeSelected: (SlotShape) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "槽位形状",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                SlotShape.values().forEach { shape ->
                    ShapeItem(
                        shape = shape,
                        isSelected = shape == selectedShape,
                        onClick = { onShapeSelected(shape) },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
private fun ShapeItem(
    shape: SlotShape,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.clickable { onClick() },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val shapeModifier = Modifier
            .size(40.dp)
            .background(
                if (isSelected) MaterialTheme.colorScheme.primary
                else MaterialTheme.colorScheme.outline,
                getShapeForSlotShape(shape)
            )

        Box(modifier = shapeModifier)

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = shape.displayName,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
private fun FillModeSection(
    selectedMode: ImageFillMode,
    onModeSelected: (ImageFillMode) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "填充方式",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            ImageFillMode.values().forEach { mode ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onModeSelected(mode) }
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = mode == selectedMode,
                        onClick = { onModeSelected(mode) }
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = mode.displayName,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }
}

@Composable
private fun StyleSection(
    config: LayoutConfig,
    onConfigChange: (LayoutConfig) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "样式设置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 间距设置
            Text(
                text = "间距: ${config.spacing.toInt()}dp",
                style = MaterialTheme.typography.bodyMedium
            )
            Slider(
                value = config.spacing,
                onValueChange = { value ->
                    onConfigChange(config.copy(spacing = value))
                },
                valueRange = 0f..32f
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 边距设置
            Text(
                text = "边距: ${config.padding.toInt()}dp",
                style = MaterialTheme.typography.bodyMedium
            )
            Slider(
                value = config.padding,
                onValueChange = { value ->
                    onConfigChange(config.copy(padding = value))
                },
                valueRange = 0f..48f
            )

            Spacer(modifier = Modifier.height(8.dp))

            // 圆角设置
            Text(
                text = "圆角: ${config.cornerRadius.toInt()}dp",
                style = MaterialTheme.typography.bodyMedium
            )
            Slider(
                value = config.cornerRadius,
                onValueChange = { value ->
                    onConfigChange(config.copy(cornerRadius = value))
                },
                valueRange = 0f..24f
            )
        }
    }
}

@Composable
private fun PreviewSection(
    config: LayoutConfig,
    modifier: Modifier = Modifier
) {
    Card(modifier = modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "预览",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 简单的预览网格
            LazyVerticalGrid(
                columns = GridCells.Fixed(config.getActualColumns()),
                modifier = Modifier.height(200.dp),
                horizontalArrangement = Arrangement.spacedBy(config.spacing.dp),
                verticalArrangement = Arrangement.spacedBy(config.spacing.dp),
                contentPadding = PaddingValues(config.padding.dp),
                userScrollEnabled = false
            ) {
                val previewCount = if (config.isDynamicRows()) {
                    config.getActualColumns() * 3 // 显示3行作为预览
                } else {
                    config.getTotalSlots()
                }

                items(previewCount) {
                    Box(
                        modifier = Modifier
                            .aspectRatio(1f)
                            .background(
                                MaterialTheme.colorScheme.outline,
                                getShapeForSlotShape(config.slotShape)
                            )
                    )
                }
            }
        }
    }
}

private fun getShapeForSlotShape(slotShape: SlotShape): Shape {
    return when (slotShape) {
        SlotShape.SQUARE -> RoundedCornerShape(4.dp)
        SlotShape.CIRCLE -> CircleShape
        SlotShape.TRIANGLE -> RoundedCornerShape(4.dp) // 简化为圆角矩形
        SlotShape.PENTAGON -> RoundedCornerShape(8.dp) // 简化为圆角矩形
    }
}
