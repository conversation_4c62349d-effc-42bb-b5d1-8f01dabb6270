package com.wtb.photocc_android.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Apps
import androidx.compose.material.icons.filled.ViewAgenda
import androidx.compose.material.icons.filled.ViewList
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.wtb.photocc_android.data.ImageItem
import com.wtb.photocc_android.data.Session
import com.wtb.photocc_android.data.ViewMode

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImageBrowserScreen(
    session: Session,
    onNavigateBack: () -> Unit,
    onViewModeChange: (ViewMode) -> Unit,
    onImageClick: (ImageItem, Int) -> Unit,
    modifier: Modifier = Modifier
) {
    var showViewModeMenu by remember { mutableStateOf(false) }
    val gridState = rememberLazyGridState()
    
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 顶部栏
        TopAppBar(
            title = { 
                Column {
                    Text(
                        text = session.getDisplayName(),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = "${session.getImageCount()} 张图片",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                Box {
                    IconButton(onClick = { showViewModeMenu = true }) {
                        Icon(
                            when (session.viewMode) {
                                ViewMode.SINGLE_COLUMN -> Icons.Default.ViewList
                                ViewMode.DOUBLE_COLUMN -> Icons.Default.ViewAgenda
                                ViewMode.MULTI_COLUMN -> Icons.Default.Apps
                            },
                            contentDescription = "切换视图模式"
                        )
                    }
                    
                    DropdownMenu(
                        expanded = showViewModeMenu,
                        onDismissRequest = { showViewModeMenu = false }
                    ) {
                        ViewMode.values().forEach { mode ->
                            DropdownMenuItem(
                                text = { Text(mode.displayName) },
                                onClick = {
                                    onViewModeChange(mode)
                                    showViewModeMenu = false
                                },
                                leadingIcon = {
                                    Icon(
                                        when (mode) {
                                            ViewMode.SINGLE_COLUMN -> Icons.Default.ViewList
                                            ViewMode.DOUBLE_COLUMN -> Icons.Default.ViewAgenda
                                            ViewMode.MULTI_COLUMN -> Icons.Default.Apps
                                        },
                                        contentDescription = null
                                    )
                                }
                            )
                        }
                    }
                }
            }
        )
        
        // 图片网格
        if (session.images.isEmpty()) {
            EmptyImagesView()
        } else {
            LazyVerticalGrid(
                columns = GridCells.Fixed(session.viewMode.columns),
                state = gridState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(session.images) { image ->
                    val index = session.images.indexOf(image)
                    ImageGridItem(
                        image = image,
                        onClick = { onImageClick(image, index) }
                    )
                }
            }
        }
    }
}

@Composable
private fun EmptyImagesView(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "暂无图片",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "选择的目录中没有找到支持的图片文件",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ImageGridItem(
    image: ImageItem,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier.aspectRatio(1f)
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(image.getUri())
                    .crossfade(true)
                    .build(),
                contentDescription = image.name,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(8.dp)),
                contentScale = ContentScale.Crop
            )
            
            // 图片名称覆盖层
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter),
                color = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f)
            ) {
                Text(
                    text = image.name,
                    modifier = Modifier.padding(8.dp),
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}
