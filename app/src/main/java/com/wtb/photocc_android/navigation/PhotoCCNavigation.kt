package com.wtb.photocc_android.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.wtb.photocc_android.ui.screens.*
import com.wtb.photocc_android.viewmodel.PhotoCCViewModel

/**
 * 导航路由定义
 */
object PhotoCCDestinations {
    const val HOME = "home"
    const val DIRECTORY_SELECTION = "directory_selection"
    const val IMAGE_BROWSER = "image_browser/{sessionId}"
    const val EXPORT_SELECTION = "export_selection/{sessionId}"
    const val EXPORT_CONFIG = "export_config/{sessionId}/{format}"
    const val LAYOUT_SELECTION = "layout_selection/{sessionId}/{format}"

    fun imageBrowser(sessionId: String) = "image_browser/$sessionId"
    fun exportSelection(sessionId: String) = "export_selection/$sessionId"
    fun exportConfig(sessionId: String, format: String) = "export_config/$sessionId/$format"
    fun layoutSelection(sessionId: String, format: String) = "layout_selection/$sessionId/$format"
}

/**
 * 主导航组件
 */
@Composable
fun PhotoCCNavigation(
    navController: NavHostController = rememberNavController(),
    viewModel: PhotoCCViewModel
) {
    NavHost(
        navController = navController,
        startDestination = PhotoCCDestinations.HOME
    ) {
        // 主页
        composable(PhotoCCDestinations.HOME) {
            HomeScreen(
                sessions = viewModel.sessions,
                onCreateNewSession = {
                    navController.navigate(PhotoCCDestinations.DIRECTORY_SELECTION)
                },
                onSessionClick = { session ->
                    viewModel.selectCurrentSession(session)
                    navController.navigate(PhotoCCDestinations.imageBrowser(session.id))
                },
                onDeleteSession = { session ->
                    viewModel.deleteSession(session.id)
                },
                onRefreshSession = { session ->
                    viewModel.refreshSession(session.id)
                }
            )
        }
        
        // 目录选择页面
        composable(PhotoCCDestinations.DIRECTORY_SELECTION) {
            DirectorySelectionScreen(
                selectedDirectories = viewModel.selectedDirectories,
                sessionName = viewModel.sessionName,
                onSessionNameChange = viewModel::updateSessionName,
                onAddDirectory = viewModel::selectDirectory,
                onRemoveDirectory = viewModel::removeDirectory,
                onCreateSession = {
                    viewModel.createSession { sessionId ->
                        navController.navigate(PhotoCCDestinations.imageBrowser(sessionId)) {
                            popUpTo(PhotoCCDestinations.HOME)
                        }
                    }
                },
                onNavigateBack = {
                    viewModel.clearDirectorySelection()
                    navController.popBackStack()
                },
                isCreating = viewModel.isCreatingSession
            )
        }
        
        // 图片浏览页面
        composable(PhotoCCDestinations.IMAGE_BROWSER) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            val currentSession = viewModel.currentSession
            
            if (currentSession != null && currentSession.id == sessionId) {
                ImageBrowserScreen(
                    session = currentSession,
                    onNavigateBack = {
                        navController.popBackStack()
                    },
                    onViewModeChange = { viewMode ->
                        viewModel.updateViewMode(viewMode)
                    },
                    onImageClick = { image, index ->
                        viewModel.updateCurrentImageIndex(index)
                        // TODO: 实现图片详情页面
                    },
                    onExportClick = {
                        navController.navigate(PhotoCCDestinations.exportSelection(sessionId))
                    }
                )
            } else {
                // 会话不存在，返回主页
                navController.navigate(PhotoCCDestinations.HOME) {
                    popUpTo(PhotoCCDestinations.HOME) {
                        inclusive = true
                    }
                }
            }
        }

        // 导出格式选择页面
        composable(PhotoCCDestinations.EXPORT_SELECTION) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            val currentSession = viewModel.currentSession

            if (currentSession != null && currentSession.id == sessionId) {
                ExportSelectionScreen(
                    imageCount = currentSession.getImageCount(),
                    onFormatSelected = { format ->
                        navController.navigate(PhotoCCDestinations.exportConfig(sessionId, format.name))
                    },
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            } else {
                navController.navigate(PhotoCCDestinations.HOME) {
                    popUpTo(PhotoCCDestinations.HOME) { inclusive = true }
                }
            }
        }

        // 导出配置页面
        composable(PhotoCCDestinations.EXPORT_CONFIG) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            val formatName = backStackEntry.arguments?.getString("format") ?: ""
            val currentSession = viewModel.currentSession

            if (currentSession != null && currentSession.id == sessionId) {
                // TODO: 实现导出配置页面
                ExportConfigScreen(
                    initialConfig = com.wtb.photocc_android.data.export.ExportConfig.createDefault(
                        com.wtb.photocc_android.data.export.ExportFormat.valueOf(formatName)
                    ),
                    onConfigChange = { /* TODO */ },
                    onStartExport = { /* TODO */ },
                    onNavigateToLayout = {
                        navController.navigate(PhotoCCDestinations.layoutSelection(sessionId, formatName))
                    },
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            } else {
                navController.navigate(PhotoCCDestinations.HOME) {
                    popUpTo(PhotoCCDestinations.HOME) { inclusive = true }
                }
            }
        }

        // 排版选择页面
        composable(PhotoCCDestinations.LAYOUT_SELECTION) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            val formatName = backStackEntry.arguments?.getString("format") ?: ""
            val currentSession = viewModel.currentSession

            if (currentSession != null && currentSession.id == sessionId) {
                LayoutSelectionScreen(
                    initialConfig = com.wtb.photocc_android.data.export.LayoutConfig(),
                    onConfigChange = { /* TODO */ },
                    onConfirm = {
                        navController.popBackStack()
                    },
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            } else {
                navController.navigate(PhotoCCDestinations.HOME) {
                    popUpTo(PhotoCCDestinations.HOME) { inclusive = true }
                }
            }
        }
    }
}
