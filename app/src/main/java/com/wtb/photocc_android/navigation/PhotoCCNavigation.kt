package com.wtb.photocc_android.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.wtb.photocc_android.ui.screens.DirectorySelectionScreen
import com.wtb.photocc_android.ui.screens.HomeScreen
import com.wtb.photocc_android.ui.screens.ImageBrowserScreen
import com.wtb.photocc_android.viewmodel.PhotoCCViewModel

/**
 * 导航路由定义
 */
object PhotoCCDestinations {
    const val HOME = "home"
    const val DIRECTORY_SELECTION = "directory_selection"
    const val IMAGE_BROWSER = "image_browser/{sessionId}"
    
    fun imageBrowser(sessionId: String) = "image_browser/$sessionId"
}

/**
 * 主导航组件
 */
@Composable
fun PhotoCCNavigation(
    navController: NavHostController = rememberNavController(),
    viewModel: PhotoCCViewModel
) {
    NavHost(
        navController = navController,
        startDestination = PhotoCCDestinations.HOME
    ) {
        // 主页
        composable(PhotoCCDestinations.HOME) {
            HomeScreen(
                sessions = viewModel.sessions,
                onCreateNewSession = {
                    navController.navigate(PhotoCCDestinations.DIRECTORY_SELECTION)
                },
                onSessionClick = { session ->
                    viewModel.setCurrentSession(session)
                    navController.navigate(PhotoCCDestinations.imageBrowser(session.id))
                },
                onDeleteSession = { session ->
                    viewModel.deleteSession(session.id)
                },
                onRefreshSession = { session ->
                    viewModel.refreshSession(session.id)
                }
            )
        }
        
        // 目录选择页面
        composable(PhotoCCDestinations.DIRECTORY_SELECTION) {
            DirectorySelectionScreen(
                selectedDirectories = viewModel.selectedDirectories,
                sessionName = viewModel.sessionName,
                onSessionNameChange = viewModel::updateSessionName,
                onAddDirectory = viewModel::selectDirectory,
                onRemoveDirectory = viewModel::removeDirectory,
                onCreateSession = {
                    viewModel.createSession { sessionId ->
                        navController.navigate(PhotoCCDestinations.imageBrowser(sessionId)) {
                            popUpTo(PhotoCCDestinations.HOME)
                        }
                    }
                },
                onNavigateBack = {
                    viewModel.clearDirectorySelection()
                    navController.popBackStack()
                },
                isCreating = viewModel.isCreatingSession
            )
        }
        
        // 图片浏览页面
        composable(PhotoCCDestinations.IMAGE_BROWSER) { backStackEntry ->
            val sessionId = backStackEntry.arguments?.getString("sessionId") ?: ""
            val currentSession = viewModel.currentSession
            
            if (currentSession != null && currentSession.id == sessionId) {
                ImageBrowserScreen(
                    session = currentSession,
                    onNavigateBack = {
                        navController.popBackStack()
                    },
                    onViewModeChange = { viewMode ->
                        viewModel.updateViewMode(viewMode)
                    },
                    onImageClick = { image, index ->
                        viewModel.updateCurrentImageIndex(index)
                        // TODO: 实现图片详情页面
                    }
                )
            } else {
                // 会话不存在，返回主页
                navController.navigate(PhotoCCDestinations.HOME) {
                    popUpTo(PhotoCCDestinations.HOME) {
                        inclusive = true
                    }
                }
            }
        }
    }
}
