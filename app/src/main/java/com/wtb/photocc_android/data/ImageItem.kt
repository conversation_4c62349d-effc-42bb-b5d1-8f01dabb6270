package com.wtb.photocc_android.data

import android.net.Uri
import kotlinx.serialization.Serializable

/**
 * 表示一个图片项目
 */
@Serializable
data class ImageItem(
    val uri: String,  // 图片的 URI 字符串
    val name: String, // 图片文件名
    val size: Long,   // 文件大小（字节）
    val lastModified: Long, // 最后修改时间
    val mimeType: String? = null // MIME 类型
) {
    /**
     * 获取 Uri 对象
     */
    fun getUri(): Uri = Uri.parse(uri)
    
    /**
     * 检查是否为支持的图片格式
     */
    fun isSupportedImageFormat(): Boolean {
        val supportedTypes = setOf(
            "image/jpeg",
            "image/jpg", 
            "image/png",
            "image/gif",
            "image/webp",
            "image/bmp"
        )
        return mimeType?.lowercase() in supportedTypes || 
               name.lowercase().let { fileName ->
                   fileName.endsWith(".jpg") || 
                   fileName.endsWith(".jpeg") || 
                   fileName.endsWith(".png") || 
                   fileName.endsWith(".gif") || 
                   fileName.endsWith(".webp") || 
                   fileName.endsWith(".bmp")
               }
    }
}
