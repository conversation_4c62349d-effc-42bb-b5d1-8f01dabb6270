package com.wtb.photocc_android.data

import kotlinx.serialization.Serializable
import java.util.UUID

/**
 * 表示一个浏览会话
 */
@Serializable
data class Session(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val directoryUris: List<String>, // 选择的目录 URI 列表
    val images: List<ImageItem> = emptyList(),
    val createdAt: Long = System.currentTimeMillis(),
    val lastAccessedAt: Long = System.currentTimeMillis(),
    val currentImageIndex: Int = 0, // 当前浏览的图片索引
    val viewMode: ViewMode = ViewMode.SINGLE_COLUMN
) {
    /**
     * 获取会话的显示名称
     */
    fun getDisplayName(): String {
        return if (name.isNotBlank()) name else "Session ${id.take(8)}"
    }
    
    /**
     * 获取图片总数
     */
    fun getImageCount(): Int = images.size
    
    /**
     * 检查是否有图片
     */
    fun hasImages(): Boolean = images.isNotEmpty()
    
    /**
     * 获取当前图片
     */
    fun getCurrentImage(): ImageItem? {
        return if (currentImageIndex in images.indices) {
            images[currentImageIndex]
        } else null
    }
    
    /**
     * 更新最后访问时间
     */
    fun updateLastAccessed(): Session {
        return copy(lastAccessedAt = System.currentTimeMillis())
    }
    
    /**
     * 更新当前图片索引
     */
    fun updateCurrentImageIndex(index: Int): Session {
        val validIndex = index.coerceIn(0, images.size - 1)
        return copy(currentImageIndex = validIndex, lastAccessedAt = System.currentTimeMillis())
    }
    
    /**
     * 更新查看模式
     */
    fun updateViewMode(mode: ViewMode): Session {
        return copy(viewMode = mode, lastAccessedAt = System.currentTimeMillis())
    }
    
    /**
     * 添加图片列表
     */
    fun updateImages(newImages: List<ImageItem>): Session {
        return copy(images = newImages, lastAccessedAt = System.currentTimeMillis())
    }
}

/**
 * 图片浏览模式
 */
@Serializable
enum class ViewMode(val displayName: String, val columns: Int) {
    SINGLE_COLUMN("单列", 1),
    DOUBLE_COLUMN("双列", 2),
    MULTI_COLUMN("多列", 3)
}
