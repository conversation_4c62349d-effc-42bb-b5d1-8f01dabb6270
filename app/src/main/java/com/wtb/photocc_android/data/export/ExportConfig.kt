package com.wtb.photocc_android.data.export

import com.wtb.photocc_android.data.ImageItem
import kotlinx.serialization.Serializable

/**
 * 导出配置
 */
@Serializable
data class ExportConfig(
    val format: ExportFormat,
    val layoutConfig: LayoutConfig? = null, // PDF、单张大图、HTML需要排版配置
    val fileName: String = "",
    val quality: Int = 90, // 图片质量 (1-100)
    val maxWidth: Int = 2048, // 最大宽度
    val maxHeight: Int = 2048, // 最大高度
    val includeMetadata: Boolean = true, // 是否包含元数据
    val compressionLevel: Int = 6 // ZIP压缩级别 (0-9)
) {
    /**
     * 是否需要排版配置
     */
    fun needsLayout(): Boolean {
        return format in listOf(
            ExportFormat.PDF,
            ExportFormat.SINGLE_IMAGE,
            ExportFormat.HTML
        )
    }
    
    /**
     * 验证配置是否有效
     */
    fun isValid(): Boolean {
        return fileName.isNotBlank() &&
               quality in 1..100 &&
               maxWidth > 0 &&
               maxHeight > 0 &&
               compressionLevel in 0..9 &&
               (!needsLayout() || (layoutConfig != null && layoutConfig.isValid()))
    }
    
    /**
     * 获取文件扩展名
     */
    fun getFileExtension(): String = format.extension
    
    /**
     * 获取完整文件名
     */
    fun getFullFileName(): String {
        val name = fileName.ifBlank { "export_${System.currentTimeMillis()}" }
        return if (name.endsWith(".${format.extension}")) {
            name
        } else {
            "$name.${format.extension}"
        }
    }
    
    companion object {
        /**
         * 创建默认配置
         */
        fun createDefault(format: ExportFormat): ExportConfig {
            return ExportConfig(
                format = format,
                layoutConfig = if (format in listOf(
                    ExportFormat.PDF,
                    ExportFormat.SINGLE_IMAGE,
                    ExportFormat.HTML
                )) LayoutConfig() else null
            )
        }
    }
}

/**
 * 导出任务状态
 */
@Serializable
enum class ExportStatus {
    PENDING,    // 等待中
    PREPARING,  // 准备中
    PROCESSING, // 处理中
    COMPLETED,  // 已完成
    FAILED,     // 失败
    CANCELLED   // 已取消
}

/**
 * 导出任务
 */
data class ExportTask(
    val id: String = java.util.UUID.randomUUID().toString(),
    val config: ExportConfig,
    val images: List<ImageItem>,
    val status: ExportStatus = ExportStatus.PENDING,
    val progress: Float = 0f,
    val message: String = "",
    val outputPath: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val startedAt: Long? = null,
    val completedAt: Long? = null,
    val error: String? = null
) {
    /**
     * 是否正在进行中
     */
    fun isInProgress(): Boolean {
        return status in listOf(
            ExportStatus.PENDING,
            ExportStatus.PREPARING,
            ExportStatus.PROCESSING
        )
    }
    
    /**
     * 是否已完成
     */
    fun isCompleted(): Boolean {
        return status == ExportStatus.COMPLETED
    }
    
    /**
     * 是否失败
     */
    fun isFailed(): Boolean {
        return status == ExportStatus.FAILED
    }
    
    /**
     * 获取耗时（毫秒）
     */
    fun getDuration(): Long? {
        return if (startedAt != null && completedAt != null) {
            completedAt - startedAt
        } else null
    }
    
    /**
     * 更新状态
     */
    fun updateStatus(
        newStatus: ExportStatus,
        newProgress: Float = progress,
        newMessage: String = message,
        newError: String? = error
    ): ExportTask {
        val now = System.currentTimeMillis()
        return copy(
            status = newStatus,
            progress = newProgress,
            message = newMessage,
            error = newError,
            startedAt = if (newStatus == ExportStatus.PREPARING && startedAt == null) now else startedAt,
            completedAt = if (newStatus in listOf(ExportStatus.COMPLETED, ExportStatus.FAILED, ExportStatus.CANCELLED)) now else null
        )
    }
}
