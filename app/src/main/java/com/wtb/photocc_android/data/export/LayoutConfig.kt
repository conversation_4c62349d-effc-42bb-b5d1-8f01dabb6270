package com.wtb.photocc_android.data.export

import kotlinx.serialization.Serializable

/**
 * 槽位形状
 */
@Serializable
enum class SlotShape(val displayName: String) {
    SQUARE("正方形"),
    CIRCLE("圆形"),
    TRIANGLE("三角形"),
    PENTAGON("五角星");
    
    companion object {
        fun getDefault() = SQUARE
    }
}

/**
 * 图片填充方式
 */
@Serializable
enum class ImageFillMode(val displayName: String) {
    CENTER_CROP("居中裁剪"),
    FIT_CENTER("适应居中"),
    STRETCH_XY("拉伸填充");
    
    companion object {
        fun getDefault() = CENTER_CROP
    }
}

/**
 * 导出格式
 */
@Serializable
enum class ExportFormat(val displayName: String, val extension: String) {
    PDF("PDF文档", "pdf"),
    SINGLE_IMAGE("单张大图", "png"),
    HTML("HTML网页", "html"),
    ZIP("ZIP压缩包", "zip"),
    EPUB("EPUB电子书", "epub");
}

/**
 * 预设模板
 */
@Serializable
enum class LayoutTemplate(
    val displayName: String,
    val columns: Int,
    val rows: Int,
    val totalSlots: Int
) {
    FOUR_GRID("4宫格", 2, 2, 4),
    NINE_GRID("9宫格", 3, 3, 9),
    SIXTEEN_GRID("16宫格", 4, 4, 16),
    SINGLE_COLUMN("单列", 1, -1, -1), // -1 表示动态行数
    DOUBLE_COLUMN("双列", 2, -1, -1),
    TRIPLE_COLUMN("三列", 3, -1, -1),
    CUSTOM("自定义", -1, -1, -1);
    
    companion object {
        fun getDefault() = NINE_GRID
    }
}

/**
 * 排版配置
 */
@Serializable
data class LayoutConfig(
    val template: LayoutTemplate = LayoutTemplate.getDefault(),
    val columns: Int = 3,
    val rows: Int = 3,
    val slotShape: SlotShape = SlotShape.getDefault(),
    val fillMode: ImageFillMode = ImageFillMode.getDefault(),
    val spacing: Float = 8f, // 间距（dp）
    val padding: Float = 16f, // 边距（dp）
    val backgroundColor: Long = 0xFFFFFFFF, // 背景色
    val cornerRadius: Float = 8f, // 圆角半径（dp）
    val showBorder: Boolean = false,
    val borderWidth: Float = 1f,
    val borderColor: Long = 0xFF000000
) {
    /**
     * 获取实际列数
     */
    fun getActualColumns(): Int {
        return when (template) {
            LayoutTemplate.CUSTOM -> columns
            else -> template.columns
        }
    }
    
    /**
     * 获取实际行数（如果是动态行数则返回-1）
     */
    fun getActualRows(): Int {
        return when (template) {
            LayoutTemplate.CUSTOM -> rows
            else -> template.rows
        }
    }
    
    /**
     * 计算总槽位数（对于固定模板）
     */
    fun getTotalSlots(): Int {
        return when (template) {
            LayoutTemplate.CUSTOM -> columns * rows
            LayoutTemplate.SINGLE_COLUMN, 
            LayoutTemplate.DOUBLE_COLUMN, 
            LayoutTemplate.TRIPLE_COLUMN -> -1 // 动态
            else -> template.totalSlots
        }
    }
    
    /**
     * 是否为动态行数模板
     */
    fun isDynamicRows(): Boolean {
        return template.rows == -1
    }
    
    /**
     * 根据图片数量计算实际行数
     */
    fun calculateRows(imageCount: Int): Int {
        if (!isDynamicRows()) {
            return getActualRows()
        }
        
        val cols = getActualColumns()
        return (imageCount + cols - 1) / cols // 向上取整
    }
    
    /**
     * 验证配置是否有效
     */
    fun isValid(): Boolean {
        return getActualColumns() > 0 && 
               (isDynamicRows() || getActualRows() > 0) &&
               spacing >= 0 &&
               padding >= 0 &&
               cornerRadius >= 0 &&
               borderWidth >= 0
    }
    
    companion object {
        /**
         * 创建预设模板配置
         */
        fun fromTemplate(template: LayoutTemplate): LayoutConfig {
            return LayoutConfig(
                template = template,
                columns = if (template.columns > 0) template.columns else 3,
                rows = if (template.rows > 0) template.rows else 3
            )
        }
    }
}
