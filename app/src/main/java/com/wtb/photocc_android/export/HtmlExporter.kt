package com.wtb.photocc_android.export

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Base64
import com.wtb.photocc_android.data.ImageItem
import com.wtb.photocc_android.data.export.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.*

class HtmlExporter(private val context: Context) {
    
    suspend fun export(
        task: ExportTask,
        outputStream: OutputStream,
        onProgress: (ExportTask) -> Unit
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val config = task.config
            val layoutConfig = config.layoutConfig 
                ?: return@withContext Result.failure(Exception("HTML导出需要排版配置"))
            
            onProgress(task.updateStatus(ExportStatus.PREPARING, 0f, "准备HTML文档..."))
            
            val images = task.images
            val htmlBuilder = StringBuilder()
            
            // 生成HTML头部
            htmlBuilder.append(generateHtmlHeader(config, layoutConfig))
            
            onProgress(task.updateStatus(ExportStatus.PROCESSING, 10f, "处理图片..."))
            
            // 生成图片网格
            htmlBuilder.append(generateImageGrid(images, config, layoutConfig, onProgress, task))
            
            // 生成HTML尾部
            htmlBuilder.append(generateHtmlFooter())
            
            onProgress(task.updateStatus(ExportStatus.PROCESSING, 95f, "保存HTML文件..."))
            
            // 写入输出流
            outputStream.write(htmlBuilder.toString().toByteArray(Charsets.UTF_8))
            
            onProgress(task.updateStatus(ExportStatus.COMPLETED, 100f, "HTML导出完成"))
            Result.success("HTML导出成功")
            
        } catch (e: Exception) {
            onProgress(task.updateStatus(ExportStatus.FAILED, 0f, "导出失败", e.message))
            Result.failure(e)
        }
    }
    
    private fun generateHtmlHeader(config: ExportConfig, layoutConfig: LayoutConfig): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val currentTime = dateFormat.format(Date())
        
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${config.fileName.ifBlank { "图片集合" }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: ${layoutConfig.padding}px;
            background-color: #${layoutConfig.backgroundColor.toString(16).takeLast(6)};
        }
        
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .title {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .subtitle {
            color: #666;
            font-size: 0.9em;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(${layoutConfig.getActualColumns()}, 1fr);
            gap: ${layoutConfig.spacing}px;
            max-width: 100%;
        }
        
        .image-item {
            position: relative;
            aspect-ratio: 1;
            overflow: hidden;
            ${getShapeStyles(layoutConfig.slotShape, layoutConfig.cornerRadius)}
            ${if (layoutConfig.showBorder) "border: ${layoutConfig.borderWidth}px solid #${layoutConfig.borderColor.toString(16).takeLast(6)};" else ""}
        }
        
        .image-item img {
            width: 100%;
            height: 100%;
            object-fit: ${getObjectFit(layoutConfig.fillMode)};
            transition: transform 0.3s ease;
        }
        
        .image-item:hover img {
            transform: scale(1.05);
        }
        
        .image-caption {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 8px;
            font-size: 0.8em;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .image-item:hover .image-caption {
            opacity: 1;
        }
        
        @media (max-width: 768px) {
            .image-grid {
                grid-template-columns: repeat(${minOf(layoutConfig.getActualColumns(), 2)}, 1fr);
            }
        }
        
        @media (max-width: 480px) {
            .image-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">${config.fileName.ifBlank { "图片集合" }}</div>
        <div class="subtitle">生成时间: $currentTime</div>
    </div>
    
    <div class="image-grid">
""".trimIndent()
    }
    
    private suspend fun generateImageGrid(
        images: List<ImageItem>,
        config: ExportConfig,
        layoutConfig: LayoutConfig,
        onProgress: (ExportTask) -> Unit,
        task: ExportTask
    ): String = withContext(Dispatchers.IO) {
        val gridBuilder = StringBuilder()
        
        for (i in images.indices) {
            val imageItem = images[i]
            val base64Image = convertImageToBase64(imageItem, config)
            
            if (base64Image != null) {
                gridBuilder.append("""
        <div class="image-item">
            <img src="data:image/jpeg;base64,$base64Image" alt="${imageItem.name}" loading="lazy">
            <div class="image-caption">${imageItem.name}</div>
        </div>
""")
            } else {
                // 添加占位符
                gridBuilder.append("""
        <div class="image-item" style="background-color: #f0f0f0; display: flex; align-items: center; justify-content: center;">
            <span style="color: #999;">无法加载图片</span>
        </div>
""")
            }
            
            val progress = 10f + (i + 1).toFloat() / images.size * 80f
            onProgress(task.updateStatus(
                ExportStatus.PROCESSING, 
                progress, 
                "处理图片 ${i + 1}/${images.size}..."
            ))
        }
        
        gridBuilder.toString()
    }
    
    private fun generateHtmlFooter(): String {
        return """
    </div>
    
    <script>
        // 添加图片点击放大功能
        document.querySelectorAll('.image-item img').forEach(img => {
            img.addEventListener('click', function() {
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                    cursor: pointer;
                `;
                
                const enlargedImg = document.createElement('img');
                enlargedImg.src = this.src;
                enlargedImg.style.cssText = `
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                `;
                
                overlay.appendChild(enlargedImg);
                document.body.appendChild(overlay);
                
                overlay.addEventListener('click', () => {
                    document.body.removeChild(overlay);
                });
            });
        });
    </script>
</body>
</html>
""".trimIndent()
    }
    
    private suspend fun convertImageToBase64(
        imageItem: ImageItem,
        config: ExportConfig
    ): String? = withContext(Dispatchers.IO) {
        try {
            val uri = Uri.parse(imageItem.uri)
            val inputStream = context.contentResolver.openInputStream(uri)
            val originalBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            if (originalBitmap != null) {
                // 调整图片大小以减少HTML文件大小
                val scaledBitmap = scaleBitmap(originalBitmap, 800, 800)
                
                val outputStream = ByteArrayOutputStream()
                scaledBitmap.compress(Bitmap.CompressFormat.JPEG, config.quality, outputStream)
                val imageBytes = outputStream.toByteArray()
                
                if (scaledBitmap != originalBitmap) {
                    originalBitmap.recycle()
                }
                scaledBitmap.recycle()
                
                Base64.encodeToString(imageBytes, Base64.NO_WRAP)
            } else {
                null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    private fun scaleBitmap(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }
        
        val scaleX = maxWidth.toFloat() / width
        val scaleY = maxHeight.toFloat() / height
        val scale = minOf(scaleX, scaleY)
        
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    private fun getShapeStyles(shape: SlotShape, cornerRadius: Float): String {
        return when (shape) {
            SlotShape.SQUARE -> "border-radius: ${cornerRadius}px;"
            SlotShape.CIRCLE -> "border-radius: 50%;"
            SlotShape.TRIANGLE, SlotShape.PENTAGON -> "border-radius: ${cornerRadius * 2}px;"
        }
    }
    
    private fun getObjectFit(fillMode: ImageFillMode): String {
        return when (fillMode) {
            ImageFillMode.CENTER_CROP -> "cover"
            ImageFillMode.FIT_CENTER -> "contain"
            ImageFillMode.STRETCH_XY -> "fill"
        }
    }
}
