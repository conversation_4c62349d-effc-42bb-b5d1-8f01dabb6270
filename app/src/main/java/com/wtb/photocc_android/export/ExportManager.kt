package com.wtb.photocc_android.export

import android.content.Context
import com.wtb.photocc_android.data.export.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import java.io.OutputStream

class ExportManager(private val context: Context) {
    
    private val _currentTask = MutableStateFlow<ExportTask?>(null)
    val currentTask: StateFlow<ExportTask?> = _currentTask.asStateFlow()
    
    private val pdfExporter = PdfExporter(context)
    private val singleImageExporter = SingleImageExporter(context)
    private val htmlExporter = HtmlExporter(context)
    private val zipExporter = ZipExporter(context)
    private val epubExporter = EpubExporter(context)
    
    suspend fun startExport(
        task: ExportTask,
        outputStream: OutputStream
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            _currentTask.value = task
            
            val result = when (task.config.format) {
                ExportFormat.PDF -> pdfExporter.export(task, outputStream, ::updateTask)
                ExportFormat.SINGLE_IMAGE -> singleImageExporter.export(task, outputStream, ::updateTask)
                ExportFormat.HTML -> htmlExporter.export(task, outputStream, ::updateTask)
                ExportFormat.ZIP -> zipExporter.export(task, outputStream, ::updateTask)
                ExportFormat.EPUB -> epubExporter.export(task, outputStream, ::updateTask)
            }
            
            result
        } catch (e: Exception) {
            val failedTask = task.updateStatus(ExportStatus.FAILED, 0f, "导出失败", e.message)
            _currentTask.value = failedTask
            Result.failure(e)
        }
    }
    
    private fun updateTask(updatedTask: ExportTask) {
        _currentTask.value = updatedTask
    }
    
    fun cancelExport() {
        _currentTask.value?.let { task ->
            if (task.isInProgress()) {
                _currentTask.value = task.updateStatus(ExportStatus.CANCELLED, task.progress, "导出已取消")
            }
        }
    }
    
    fun clearCurrentTask() {
        _currentTask.value = null
    }
    
    fun isExporting(): Boolean {
        return _currentTask.value?.isInProgress() == true
    }
    
    companion object {
        /**
         * 验证导出配置
         */
        fun validateExportConfig(config: ExportConfig): List<String> {
            val errors = mutableListOf<String>()
            
            if (config.fileName.isBlank()) {
                errors.add("文件名不能为空")
            }
            
            if (config.quality !in 1..100) {
                errors.add("图片质量必须在1-100之间")
            }
            
            if (config.maxWidth <= 0 || config.maxHeight <= 0) {
                errors.add("图片尺寸必须大于0")
            }
            
            if (config.compressionLevel !in 0..9) {
                errors.add("压缩级别必须在0-9之间")
            }
            
            if (config.needsLayout() && config.layoutConfig == null) {
                errors.add("${config.format.displayName}需要排版配置")
            }
            
            config.layoutConfig?.let { layoutConfig ->
                if (!layoutConfig.isValid()) {
                    errors.add("排版配置无效")
                }
            }
            
            return errors
        }
        
        /**
         * 估算导出文件大小
         */
        fun estimateFileSize(config: ExportConfig, imageCount: Int): String {
            val avgImageSize = when (config.format) {
                ExportFormat.PDF -> 200 * 1024 // 200KB per image
                ExportFormat.SINGLE_IMAGE -> 2 * 1024 * 1024 // 2MB for combined image
                ExportFormat.HTML -> 150 * 1024 // 150KB per image (base64 encoded)
                ExportFormat.ZIP -> 500 * 1024 // 500KB per image (original)
                ExportFormat.EPUB -> 300 * 1024 // 300KB per image
            }
            
            val totalSize = when (config.format) {
                ExportFormat.SINGLE_IMAGE -> avgImageSize
                else -> avgImageSize * imageCount
            }
            
            return formatFileSize(totalSize.toLong())
        }
        
        private fun formatFileSize(bytes: Long): String {
            val kb = bytes / 1024.0
            val mb = kb / 1024.0
            val gb = mb / 1024.0
            
            return when {
                gb >= 1 -> String.format("%.1f GB", gb)
                mb >= 1 -> String.format("%.1f MB", mb)
                kb >= 1 -> String.format("%.1f KB", kb)
                else -> "$bytes B"
            }
        }
    }
}
