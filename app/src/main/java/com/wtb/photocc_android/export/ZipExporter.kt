package com.wtb.photocc_android.export

import android.content.Context
import android.net.Uri
import com.wtb.photocc_android.data.ImageItem
import com.wtb.photocc_android.data.export.ExportConfig
import com.wtb.photocc_android.data.export.ExportTask
import com.wtb.photocc_android.data.export.ExportStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.lingala.zip4j.ZipFile
import net.lingala.zip4j.model.ZipParameters
import net.lingala.zip4j.model.enums.CompressionLevel
import net.lingala.zip4j.model.enums.CompressionMethod
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream

class ZipExporter(private val context: Context) {
    
    suspend fun export(
        task: ExportTask,
        outputStream: OutputStream,
        onProgress: (ExportTask) -> Unit
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val config = task.config
            val images = task.images
            
            onProgress(task.updateStatus(ExportStatus.PREPARING, 0f, "准备ZIP文件..."))
            
            // 创建临时目录
            val tempDir = File(context.cacheDir, "export_temp_${System.currentTimeMillis()}")
            tempDir.mkdirs()
            
            try {
                val tempZipFile = File(tempDir, "temp.zip")
                val zipFile = ZipFile(tempZipFile)
                
                // 设置压缩参数
                val zipParameters = ZipParameters().apply {
                    compressionMethod = CompressionMethod.DEFLATE
                    compressionLevel = getCompressionLevel(config.compressionLevel)
                }
                
                onProgress(task.updateStatus(ExportStatus.PROCESSING, 10f, "添加图片到ZIP..."))
                
                // 添加每张图片到ZIP
                for (i in images.indices) {
                    val imageItem = images[i]
                    val success = addImageToZip(zipFile, imageItem, zipParameters, i + 1)
                    
                    if (!success) {
                        onProgress(task.updateStatus(
                            ExportStatus.PROCESSING, 
                            10f + (i + 1).toFloat() / images.size * 80f,
                            "跳过无效图片: ${imageItem.name}"
                        ))
                        continue
                    }
                    
                    val progress = 10f + (i + 1).toFloat() / images.size * 80f
                    onProgress(task.updateStatus(
                        ExportStatus.PROCESSING, 
                        progress, 
                        "添加图片 ${i + 1}/${images.size}: ${imageItem.name}"
                    ))
                }
                
                onProgress(task.updateStatus(ExportStatus.PROCESSING, 95f, "完成ZIP文件..."))
                
                // 将临时ZIP文件复制到输出流
                tempZipFile.inputStream().use { input ->
                    input.copyTo(outputStream)
                }
                
                onProgress(task.updateStatus(ExportStatus.COMPLETED, 100f, "ZIP导出完成"))
                Result.success("ZIP导出成功，包含 ${images.size} 张图片")
                
            } finally {
                // 清理临时文件
                tempDir.deleteRecursively()
            }
            
        } catch (e: Exception) {
            onProgress(task.updateStatus(ExportStatus.FAILED, 0f, "导出失败", e.message))
            Result.failure(e)
        }
    }
    
    private suspend fun addImageToZip(
        zipFile: ZipFile,
        imageItem: ImageItem,
        zipParameters: ZipParameters,
        index: Int
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            val uri = Uri.parse(imageItem.uri)
            val inputStream = context.contentResolver.openInputStream(uri)
            
            if (inputStream != null) {
                // 生成安全的文件名
                val safeFileName = generateSafeFileName(imageItem.name, index)
                zipParameters.fileNameInZip = safeFileName
                
                zipFile.addStream(inputStream, zipParameters)
                inputStream.close()
                true
            } else {
                false
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    private fun generateSafeFileName(originalName: String, index: Int): String {
        // 移除或替换不安全的字符
        val safeName = originalName
            .replace(Regex("[<>:\"/\\\\|?*]"), "_")
            .replace(Regex("\\s+"), "_")
            .trim()
        
        // 如果文件名为空或只包含特殊字符，使用默认名称
        return if (safeName.isBlank()) {
            "image_$index.jpg"
        } else {
            // 确保有文件扩展名
            if (safeName.contains('.')) {
                safeName
            } else {
                "$safeName.jpg"
            }
        }
    }
    
    private fun getCompressionLevel(level: Int): CompressionLevel {
        return when (level) {
            0 -> CompressionLevel.NO_COMPRESSION
            1 -> CompressionLevel.FASTEST
            2, 3 -> CompressionLevel.FAST
            4, 5, 6 -> CompressionLevel.NORMAL
            7, 8 -> CompressionLevel.MAXIMUM
            9 -> CompressionLevel.ULTRA
            else -> CompressionLevel.NORMAL
        }
    }
}
