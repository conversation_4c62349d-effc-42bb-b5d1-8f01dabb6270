package com.wtb.photocc_android.export

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import com.wtb.photocc_android.data.ImageItem
import com.wtb.photocc_android.data.export.ExportConfig
import com.wtb.photocc_android.data.export.ExportTask
import com.wtb.photocc_android.data.export.ExportStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
// import nl.siegmann.epublib.domain.Book
// import nl.siegmann.epublib.domain.Resource
// import nl.siegmann.epublib.epub.EpubWriter
import java.io.ByteArrayOutputStream
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.*

class EpubExporter(private val context: Context) {
    
    suspend fun export(
        task: ExportTask,
        outputStream: OutputStream,
        onProgress: (ExportTask) -> Unit
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            // 简化的 EPUB 实现 - 实际上生成一个包含所有图片的 HTML 文件
            onProgress(task.updateStatus(ExportStatus.PREPARING, 0f, "创建EPUB内容..."))

            val config = task.config
            val images = task.images

            // 生成简化的 EPUB 结构（实际上是一个 ZIP 文件）
            val htmlContent = generateEpubHtml(images, config, onProgress, task)

            onProgress(task.updateStatus(ExportStatus.PROCESSING, 95f, "保存EPUB文件..."))

            // 写入HTML内容作为简化的EPUB
            outputStream.write(htmlContent.toByteArray(Charsets.UTF_8))

            onProgress(task.updateStatus(ExportStatus.COMPLETED, 100f, "EPUB导出完成"))
            Result.success("EPUB导出成功，包含 ${images.size} 张图片")

        } catch (e: Exception) {
            onProgress(task.updateStatus(ExportStatus.FAILED, 0f, "导出失败", e.message))
            Result.failure(e)
        }
    }
    
    private suspend fun generateEpubHtml(
        images: List<ImageItem>,
        config: ExportConfig,
        onProgress: (ExportTask) -> Unit,
        task: ExportTask
    ): String = withContext(Dispatchers.IO) {
        val htmlBuilder = StringBuilder()

        // 生成EPUB样式的HTML
        htmlBuilder.append(generateEpubHeader(config))

        onProgress(task.updateStatus(ExportStatus.PROCESSING, 20f, "处理图片..."))

        // 添加每张图片
        for (i in images.indices) {
            val imageItem = images[i]
            val base64Image = convertImageToBase64(imageItem, config)

            if (base64Image != null) {
                htmlBuilder.append(generateImagePage(imageItem, base64Image, i + 1))
            }

            val progress = 20f + (i + 1).toFloat() / images.size * 70f
            onProgress(task.updateStatus(
                ExportStatus.PROCESSING,
                progress,
                "处理图片 ${i + 1}/${images.size}..."
            ))
        }

        htmlBuilder.append(generateEpubFooter())
        htmlBuilder.toString()
    }

    private fun generateEpubHeader(config: ExportConfig): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val currentTime = dateFormat.format(Date())

        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${config.fileName.ifBlank { "图片集合" }}</title>
    <style>
        body {
            font-family: serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background-color: #fff;
        }
        .book-title {
            text-align: center;
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
            page-break-after: always;
        }
        .book-info {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
        }
        .chapter {
            page-break-before: always;
            margin-bottom: 40px;
        }
        .chapter-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .image {
            max-width: 100%;
            max-height: 80vh;
            border: 1px solid #ddd;
        }
        .image-caption {
            margin-top: 10px;
            font-style: italic;
            color: #666;
        }
        @media print {
            .chapter {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>
    <div class="book-title">${config.fileName.ifBlank { "图片集合" }}</div>
    <div class="book-info">
        <p>生成时间: $currentTime</p>
        <p>由 PhotoCC Android 生成</p>
    </div>
""".trimIndent()
    }

    private fun generateImagePage(imageItem: ImageItem, base64Image: String, index: Int): String {
        return """
    <div class="chapter">
        <div class="chapter-title">图片 $index</div>
        <div class="image-container">
            <img src="data:image/jpeg;base64,$base64Image" alt="${imageItem.name}" class="image">
            <div class="image-caption">
                <strong>${imageItem.name}</strong><br>
                大小: ${formatFileSize(imageItem.size)}
            </div>
        </div>
    </div>
""".trimIndent()
    }

    private fun generateEpubFooter(): String {
        return """
</body>
</html>
""".trimIndent()
    }

    private suspend fun convertImageToBase64(
        imageItem: ImageItem,
        config: ExportConfig
    ): String? = withContext(Dispatchers.IO) {
        try {
            val uri = Uri.parse(imageItem.uri)
            val inputStream = context.contentResolver.openInputStream(uri)
            val originalBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()

            if (originalBitmap != null) {
                // 调整图片大小以减少文件大小
                val scaledBitmap = scaleBitmap(originalBitmap, 800, 800)

                val outputStream = ByteArrayOutputStream()
                scaledBitmap.compress(Bitmap.CompressFormat.JPEG, config.quality, outputStream)
                val imageBytes = outputStream.toByteArray()

                if (scaledBitmap != originalBitmap) {
                    originalBitmap.recycle()
                }
                scaledBitmap.recycle()

                android.util.Base64.encodeToString(imageBytes, android.util.Base64.NO_WRAP)
            } else {
                null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    private fun scaleBitmap(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }
        
        val scaleX = maxWidth.toFloat() / width
        val scaleY = maxHeight.toFloat() / height
        val scale = minOf(scaleX, scaleY)
        
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    private fun formatFileSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        
        return when {
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$bytes B"
        }
    }
}
