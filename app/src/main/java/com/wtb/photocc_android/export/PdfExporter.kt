package com.wtb.photocc_android.export

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.PdfWriter
import com.itextpdf.layout.Document
import com.itextpdf.layout.element.Image
import com.itextpdf.layout.element.Table
import com.itextpdf.layout.properties.UnitValue
import com.itextpdf.io.image.ImageDataFactory
import com.wtb.photocc_android.data.ImageItem
import com.wtb.photocc_android.data.export.ExportConfig
import com.wtb.photocc_android.data.export.ExportTask
import com.wtb.photocc_android.data.export.ExportStatus
import com.wtb.photocc_android.data.export.LayoutConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.OutputStream

class PdfExporter(private val context: Context) {
    
    suspend fun export(
        task: ExportTask,
        outputStream: OutputStream,
        onProgress: (ExportTask) -> Unit
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val config = task.config
            val layoutConfig = config.layoutConfig 
                ?: return@withContext Result.failure(Exception("PDF导出需要排版配置"))
            
            onProgress(task.updateStatus(ExportStatus.PREPARING, 0f, "准备PDF文档..."))
            
            val pdfWriter = PdfWriter(outputStream)
            val pdfDocument = PdfDocument(pdfWriter)
            val document = Document(pdfDocument)
            
            onProgress(task.updateStatus(ExportStatus.PROCESSING, 10f, "处理图片..."))
            
            // 根据排版配置创建表格
            val columns = layoutConfig.getActualColumns()
            val table = Table(UnitValue.createPercentArray(columns))
            table.setWidth(UnitValue.createPercentValue(100f))
            
            val images = task.images
            var processedCount = 0
            
            for (i in images.indices step columns) {
                for (j in 0 until columns) {
                    val imageIndex = i + j
                    
                    if (imageIndex < images.size) {
                        val imageItem = images[imageIndex]
                        val bitmap = loadAndProcessBitmap(imageItem, config)
                        
                        if (bitmap != null) {
                            val imageBytes = bitmapToByteArray(bitmap, config.quality)
                            val imageData = ImageDataFactory.create(imageBytes)
                            val pdfImage = Image(imageData)
                            
                            // 设置图片大小
                            val cellWidth = (pdfDocument.defaultPageSize.width - 72) / columns // 72是页边距
                            pdfImage.setWidth(cellWidth)
                            pdfImage.setAutoScale(true)
                            
                            table.addCell(pdfImage)
                            bitmap.recycle()
                        } else {
                            table.addCell("") // 空单元格
                        }
                    } else {
                        table.addCell("") // 空单元格
                    }
                    
                    processedCount++
                    val progress = 10f + (processedCount.toFloat() / (images.size + columns - 1)) * 80f
                    onProgress(task.updateStatus(
                        ExportStatus.PROCESSING, 
                        progress, 
                        "处理图片 $processedCount/${images.size}..."
                    ))
                }
            }
            
            document.add(table as com.itextpdf.layout.element.IBlockElement)
            document.close()
            
            onProgress(task.updateStatus(ExportStatus.COMPLETED, 100f, "PDF导出完成"))
            Result.success("PDF导出成功")
            
        } catch (e: Exception) {
            onProgress(task.updateStatus(ExportStatus.FAILED, 0f, "导出失败", e.message))
            Result.failure(e)
        }
    }
    
    private suspend fun loadAndProcessBitmap(
        imageItem: ImageItem,
        config: ExportConfig
    ): Bitmap? = withContext(Dispatchers.IO) {
        try {
            val uri = Uri.parse(imageItem.uri)
            val inputStream = context.contentResolver.openInputStream(uri)
            val originalBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            if (originalBitmap != null) {
                // 调整大小
                val scaledBitmap = scaleBitmap(originalBitmap, config.maxWidth, config.maxHeight)
                if (scaledBitmap != originalBitmap) {
                    originalBitmap.recycle()
                }
                scaledBitmap
            } else {
                null
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    private fun scaleBitmap(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxWidth && height <= maxHeight) {
            return bitmap
        }
        
        val scaleX = maxWidth.toFloat() / width
        val scaleY = maxHeight.toFloat() / height
        val scale = minOf(scaleX, scaleY)
        
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
    }
    
    private fun bitmapToByteArray(bitmap: Bitmap, quality: Int): ByteArray {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
        return outputStream.toByteArray()
    }
}
